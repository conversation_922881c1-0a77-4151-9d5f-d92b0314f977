//
//  ScratchCardResponsiveTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡响应式布局测试视图
 * 用于验证修复后的刮刮卡网格布局在不同屏幕尺寸下的表现
 */
struct ScratchCardResponsiveTestView: View {
    
    // MARK: - State
    @State private var sampleCards: [ScratchCardItem] = []
    @State private var currentOrientation = "竖屏"
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                VStack(spacing: 20) {
                    // 设备信息显示
                    deviceInfoSection(geometry: geometry)
                    
                    // 刮刮卡网格
                    ScratchCardGridView(
                        cardItems: sampleCards,
                        geometry: geometry,
                        onCardTapped: { index in
                            print("测试：点击了卡片 \(index)")
                        }
                    )
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("刮刮卡响应式测试")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                setupSampleCards()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
                updateOrientationInfo()
            }
        }
    }
    
    // MARK: - Device Info Section
    
    private func deviceInfoSection(geometry: GeometryProxy) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("设备信息")
                .font(.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("设备类型:")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    Text(DeviceDetection.isPad ? "iPad" : "iPhone")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                HStack {
                    Text("屏幕尺寸:")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    Text("\(Int(geometry.size.width)) × \(Int(geometry.size.height))")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                HStack {
                    Text("方向:")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    Text(currentOrientation)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                HStack {
                    Text("可用宽度:")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    Text("\(Int(geometry.size.width - 40)) px")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignSystem.Colors.cardBackground)
                .shadow(color: DesignSystem.Shadow.light, radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Helper Methods
    
    private func setupSampleCards() {
        sampleCards = [
            ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
            ScratchCardItem.create(index: 1, prizeName: "铅笔"),
            ScratchCardItem.create(index: 2, prizeName: "橡皮"),
            ScratchCardItem.create(index: 3, prizeName: "尺子"),
            ScratchCardItem.create(index: 4, prizeName: "谢谢参与"),
            ScratchCardItem.create(index: 5, prizeName: "小玩具")
        ]
    }
    
    private func updateOrientationInfo() {
        DispatchQueue.main.async {
            currentOrientation = DeviceDetection.isLandscape ? "横屏" : "竖屏"
        }
    }
}

// MARK: - Preview

#Preview {
    ScratchCardResponsiveTestView()
}
