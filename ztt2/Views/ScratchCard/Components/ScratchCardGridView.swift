//
//  ScratchCardGridView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡网格视图组件
 * 实现多张刮刮卡的网格布局和单卡显示
 */
struct ScratchCardGridView: View {

    // MARK: - Properties
    let cardItems: [ScratchCardItem]
    let geometry: GeometryProxy
    let onCardTapped: (Int) -> Void

    // MARK: - State
    @State private var gridAppeared = false
    @State private var itemSize: CGSize = CGSize(width: 120, height: 160)

    // MARK: - Constants
    private let spacing: CGFloat = 12
    private let horizontalPadding: CGFloat = 20
    private let columnsCount: Int = 3 // 强制每行3张
    private let cardRatio: CGFloat = 3.0/4.0 // 宽高比：3:4

    // 总的水平边距：ScratchCardView中的20px + ScratchCardGridView中的20px = 40px
    private let totalHorizontalPadding: CGFloat = 40

    // MARK: - Computed Properties

    /**
     * 响应式网格列配置（强制3列）
     */
    private var gridColumns: [GridItem] {
        let availableWidth = geometry.size.width - totalHorizontalPadding
        let calculatedSize = calculateCardSize(for: availableWidth)

        // 更新项目大小
        DispatchQueue.main.async {
            if itemSize != calculatedSize {
                itemSize = calculatedSize
            }
        }

        return Array(repeating: GridItem(.fixed(calculatedSize.width), spacing: spacing), count: columnsCount)
    }

    /**
     * 计算卡片尺寸（强制3列布局，完全自适应）
     */
    private func calculateCardSize(for width: CGFloat) -> CGSize {
        // 计算3列布局的卡片尺寸
        let totalSpacing = CGFloat(columnsCount - 1) * spacing
        let availableWidth = width - totalSpacing
        let cardWidth = availableWidth / CGFloat(columnsCount)

        // 确保卡片宽度不会小于合理值，但优先保证不超出屏幕
        let minWidth: CGFloat = 60.0  // 设置一个更小的最小值，确保卡片不会太小
        let finalCardWidth = max(minWidth, cardWidth)
        let finalCardHeight = finalCardWidth / cardRatio

        return CGSize(width: finalCardWidth, height: finalCardHeight)
    }
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: gridColumns, spacing: spacing) {
                ForEach(Array(cardItems.enumerated()), id: \.element.id) { index, cardItem in
                    scratchCardItemView(cardItem: cardItem, index: index)
                        .opacity(gridAppeared ? 1.0 : 0.0)
                        .offset(y: gridAppeared ? 0 : 30)
                        .animation(
                            .easeOut(duration: 0.6)
                            .delay(Double(index) * 0.1),
                            value: gridAppeared
                        )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
        .onAppear {
            withAnimation {
                gridAppeared = true
            }
        }
        .onDisappear {
            gridAppeared = false
        }
    }
    
    // MARK: - Scratch Card Item View
    
    /**
     * 单张刮刮卡项目视图
     */
    private func scratchCardItemView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 8) {
            // 刮刮卡主体
            ScratchCardItemView(
                cardItem: cardItem,
                size: itemSize,
                onTap: {
                    onCardTapped(index)
                }
            )
            .floating(
                offset: CGFloat.random(in: 4...8),
                rotation: Double.random(in: 1...4),
                delay: Double(index) * 0.2,
                duration: Double.random(in: 2.0...2.8),
                enabled: !cardItem.isScratched && cardItem.animationState == .idle
            )
            
            // 刮刮卡信息
            cardInfoView(cardItem: cardItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    /**
     * 卡片信息视图
     */
    private func cardInfoView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 卡片编号
            Text(cardItem.displayTitle)
                .font(.system(size: 13, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            // 状态指示器
            statusIndicator(for: cardItem)
        }
        .frame(maxWidth: itemSize.width)
    }
    
    /**
     * 状态指示器
     */
    @ViewBuilder
    private func statusIndicator(for cardItem: ScratchCardItem) -> some View {
        HStack(spacing: 4) {
            // 状态图标
            Image(systemName: statusIcon(for: cardItem))
                .font(.system(size: 10, weight: .semibold))
                .foregroundColor(statusColor(for: cardItem))

            // 状态文本
            Text(statusText(for: cardItem))
                .font(.system(size: 10, weight: .semibold))
                .foregroundColor(statusColor(for: cardItem))
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 5)
        .background(
            Capsule()
                .fill(
                    LinearGradient(
                        colors: [
                            statusColor(for: cardItem).opacity(0.15),
                            statusColor(for: cardItem).opacity(0.08)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .overlay(
            Capsule()
                .stroke(
                    LinearGradient(
                        colors: [
                            statusColor(for: cardItem).opacity(0.6),
                            statusColor(for: cardItem).opacity(0.4)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
        .shadow(
            color: statusColor(for: cardItem).opacity(0.3),
            radius: 3,
            x: 0,
            y: 1
        )
    }
    
    // MARK: - Status Helpers

    /**
     * 获取状态图标
     */
    private func statusIcon(for cardItem: ScratchCardItem) -> String {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? "checkmark.circle.fill" : "hand.point.up.left"
        case .selected:
            return "hand.tap"
        case .scratching:
            return "hand.draw"
        case .revealing:
            return "sparkles"
        case .completed:
            return "trophy.fill"
        }
    }

    /**
     * 获取状态颜色
     */
    private func statusColor(for cardItem: ScratchCardItem) -> Color {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? Color.green : Color(hex: "#a9d051")
        case .selected:
            return Color.orange
        case .scratching:
            return Color.blue
        case .revealing:
            return Color.purple
        case .completed:
            return Color.green
        }
    }

    /**
     * 获取状态文本
     */
    private func statusText(for cardItem: ScratchCardItem) -> String {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? "scratch_card.status.scratched".localized : "scratch_card.status.available".localized
        case .selected:
            return "scratch_card.status.selected".localized
        case .scratching:
            return "scratch_card.status.scratching".localized
        case .revealing:
            return "scratch_card.status.revealing".localized
        case .completed:
            return "scratch_card.status.completed".localized
        }
    }
}

/**
 * 单张刮刮卡项目视图
 */
struct ScratchCardItemView: View {
    
    // MARK: - Properties
    let cardItem: ScratchCardItem
    let size: CGSize
    let onTap: () -> Void
    
    // MARK: - State Properties
    @State private var isPressed = false
    
    var body: some View {
        ZStack {
            // 卡片背景
            cardBackground
            
            // 卡片内容
            cardContent
            
            // 刮除状态覆盖层
            if cardItem.isScratched {
                scratchedOverlay
            }
        }
        .frame(width: size.width, height: size.height)
        .scaleEffect(cardItem.scaleEffect * (isPressed ? 0.95 : 1.0))
        .opacity(cardItem.opacity)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .animation(.easeInOut(duration: 0.6), value: cardItem.animationState)
        .onTapGesture {
            if cardItem.isClickable {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onTap()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing && cardItem.isClickable
        }, perform: {})
    }
    
    // MARK: - Card Background

    private var cardBackground: some View {
        ZStack {
            // 主背景渐变
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: cardItem.isScratched ? [
                            Color(hex: "#e8f5e8"),
                            Color(hex: "#f0f9f0")
                        ] : [
                            Color(hex: "#ffeef0"),
                            Color(hex: "#fff8e1")
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )

            // 光泽效果层
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            Color.clear,
                            Color.clear,
                            Color.white.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )

            // 边框
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: cardItem.isScratched ? [
                            Color.green.opacity(0.4),
                            Color.green.opacity(0.2)
                        ] : [
                            Color(hex: "#ff6b6b").opacity(0.3),
                            Color(hex: "#a9d051").opacity(0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        }
        .shadow(
            color: cardItem.isScratched ?
                Color.green.opacity(0.2) : Color.black.opacity(0.12),
            radius: cardItem.isScratched ? 8 : 6,
            x: 0,
            y: cardItem.isScratched ? 4 : 3
        )
    }
    
    // MARK: - Card Content
    
    private var cardContent: some View {
        VStack(spacing: 8) {
            // 刮刮卡图标
            if cardItem.isScratched {
                prizeContent
            } else {
                scratchableContent
            }
        }
        .padding(12)
    }
    
    /**
     * 奖品内容
     */
    private var prizeContent: some View {
        VStack(spacing: 8) {
            // 开启的宝箱图标背景
            ZStack {
                // 庆祝光晕背景
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.green.opacity(0.4),
                                Color.yellow.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 8,
                            endRadius: 30
                        )
                    )
                    .frame(width: size.width * 0.7, height: size.width * 0.7)

                // 开启的宝箱图标
                Image("刮刮卡中奖")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size.width * 0.5, height: size.width * 0.5)
                    .shadow(color: Color.green.opacity(0.3), radius: 3, x: 0, y: 2)

                // 闪烁星星效果
                ForEach(0..<3, id: \.self) { index in
                    Image(systemName: "sparkle")
                        .font(.system(size: 6, weight: .bold))
                        .foregroundColor(Color.yellow)
                        .offset(
                            x: CGFloat([10, -10, 0][index]),
                            y: CGFloat([-10, -8, -12][index])
                        )
                        .opacity(0.8)
                        .animation(
                            .easeInOut(duration: 1.5)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.3),
                            value: cardItem.isScratched
                        )
                }
            }

            // 中奖标识
            VStack(spacing: 2) {
                Text("scratch_card.status.won".localized)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                Color.green,
                                Color(hex: "#32d74b")
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("scratch_card.status.congratulations".localized)
                    .font(.system(size: 8, weight: .medium))
                    .foregroundColor(Color.green.opacity(0.7))
            }
        }
    }
    
    /**
     * 可刮除状态内容
     */
    private var scratchableContent: some View {
        VStack(spacing: 10) {
            // 神秘礼盒图标背景
            ZStack {
                // 背景光晕
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color(hex: "#ff6b6b").opacity(0.3),
                                Color(hex: "#a9d051").opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 5,
                            endRadius: 25
                        )
                    )
                    .frame(width: size.width * 0.6, height: size.width * 0.6)

                // 宝箱图标
                Image("刮刮卡")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size.width * 0.5, height: size.width * 0.5)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
            }

            // 刮除提示文字
            VStack(spacing: 2) {
                Text("scratch_card.hint_text".localized)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                Color(hex: "#ff6b6b"),
                                Color(hex: "#a9d051")
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("scratch_card.status.prize_reveal".localized)
                    .font(.system(size: 8, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
    }
    
    // MARK: - Scratched Overlay

    private var scratchedOverlay: some View {
        ZStack {
            // 成功状态背景
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.green.opacity(0.15),
                            Color.yellow.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )

            // 成功边框动画
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.green,
                            Color.yellow,
                            Color.green
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 3
                )
                .opacity(0.8)

            // 边角装饰星星
            VStack {
                HStack {
                    Image(systemName: "star.fill")
                        .font(.system(size: 8))
                        .foregroundColor(Color.yellow)
                    Spacer()
                    Image(systemName: "star.fill")
                        .font(.system(size: 6))
                        .foregroundColor(Color.green)
                }
                Spacer()
                HStack {
                    Image(systemName: "star.fill")
                        .font(.system(size: 6))
                        .foregroundColor(Color.green)
                    Spacer()
                    Image(systemName: "star.fill")
                        .font(.system(size: 8))
                        .foregroundColor(Color.yellow)
                }
            }
            .padding(8)
            .opacity(0.7)
        }
        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: cardItem.isScratched)
    }
}

#Preview {
    let sampleCards = [
        ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
        ScratchCardItem.create(index: 1, prizeName: "铅笔"),
        ScratchCardItem.create(index: 2, prizeName: "橡皮"),
        ScratchCardItem.create(index: 3, prizeName: "尺子"),
        ScratchCardItem.create(index: 4, prizeName: "谢谢参与"),
        ScratchCardItem.create(index: 5, prizeName: "小玩具")
    ]

    return GeometryReader { geometry in
        ScratchCardGridView(
            cardItems: sampleCards,
            geometry: geometry,
            onCardTapped: { index in
                print("Tapped card \(index)")
            }
        )
    }
    .padding()
}
