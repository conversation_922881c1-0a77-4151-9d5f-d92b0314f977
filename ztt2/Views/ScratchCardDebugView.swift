//
//  ScratchCardDebugView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡布局调试视图
 * 用于分析布局计算问题
 */
struct ScratchCardDebugView: View {
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ScrollView {
                    VStack(spacing: 20) {
                        // 调试信息
                        debugInfoSection(geometry: geometry)
                        
                        // 模拟网格布局计算
                        simulatedGridSection(geometry: geometry)
                        
                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("刮刮卡布局调试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func debugInfoSection(geometry: GeometryProxy) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("布局调试信息")
                .font(.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            let screenWidth = geometry.size.width
            let totalHorizontalPadding: CGFloat = 40  // ScratchCardView(20px) + ScratchCardGridView(20px)
            let spacing: CGFloat = 12
            let columnsCount = 3

            let availableWidth = screenWidth - totalHorizontalPadding
            let totalSpacing = CGFloat(columnsCount - 1) * spacing
            let remainingWidth = availableWidth - totalSpacing
            let calculatedCardWidth = remainingWidth / CGFloat(columnsCount)

            let minWidth: CGFloat = 60.0  // 新的最小宽度限制
            let finalCardWidth = max(minWidth, calculatedCardWidth)

            let totalUsedWidth = (finalCardWidth * CGFloat(columnsCount)) + totalSpacing + totalHorizontalPadding
            
            VStack(alignment: .leading, spacing: 8) {
                debugRow("屏幕总宽度", "\(Int(screenWidth)) px")
                debugRow("总水平内边距", "\(Int(totalHorizontalPadding)) px (双层padding)")
                debugRow("可用宽度", "\(Int(availableWidth)) px")
                debugRow("列间距总和", "\(Int(totalSpacing)) px (\(columnsCount-1) × \(Int(spacing))px)")
                debugRow("剩余宽度", "\(Int(remainingWidth)) px")
                debugRow("计算卡片宽度", "\(Int(calculatedCardWidth)) px")
                debugRow("最小宽度限制", "\(Int(minWidth)) px")
                debugRow("最终卡片宽度", "\(Int(finalCardWidth)) px")
                debugRow("总占用宽度", "\(Int(totalUsedWidth)) px")
                
                if totalUsedWidth > screenWidth {
                    Text("⚠️ 警告：总占用宽度超出屏幕！")
                        .foregroundColor(.red)
                        .fontWeight(.bold)
                } else {
                    Text("✅ 布局正常：未超出屏幕")
                        .foregroundColor(.green)
                        .fontWeight(.bold)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignSystem.Colors.cardBackground)
                .shadow(color: DesignSystem.Shadow.light, radius: 4, x: 0, y: 2)
        )
    }
    
    private func debugRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            Spacer()
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    private func simulatedGridSection(geometry: GeometryProxy) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("模拟网格布局")
                .font(.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            let screenWidth = geometry.size.width
            let totalHorizontalPadding: CGFloat = 40  // 修正后的总padding
            let spacing: CGFloat = 12
            let columnsCount = 3

            let availableWidth = screenWidth - totalHorizontalPadding
            let totalSpacing = CGFloat(columnsCount - 1) * spacing
            let remainingWidth = availableWidth - totalSpacing
            let calculatedCardWidth = remainingWidth / CGFloat(columnsCount)

            // 使用与实际代码相同的最小宽度限制
            let minWidth: CGFloat = 60.0
            let finalCardWidth = max(minWidth, calculatedCardWidth)
            let cardHeight = finalCardWidth / (3.0/4.0)
            
            LazyVGrid(columns: Array(repeating: GridItem(.fixed(finalCardWidth), spacing: spacing), count: columnsCount), spacing: spacing) {
                ForEach(0..<6, id: \.self) { index in
                    Rectangle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(width: finalCardWidth, height: cardHeight)
                        .overlay(
                            Text("卡片 \(index + 1)")
                                .font(.caption)
                                .foregroundColor(.blue)
                        )
                }
            }
            .padding(.horizontal, 20)  // 只在GridView中添加20px padding
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignSystem.Colors.cardBackground)
                .shadow(color: DesignSystem.Shadow.light, radius: 4, x: 0, y: 2)
        )
    }
}

#Preview {
    ScratchCardDebugView()
}
